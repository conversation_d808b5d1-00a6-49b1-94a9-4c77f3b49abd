<?php

// Test script for JSON Database
require_once 'config.php';
require_once 'JsonDatabase.php';

// Initialize database
$db = new JsonDatabase($JSON_DB_DIR);

echo "=== JSON Database Test ===\n\n";

// Test 1: Save a test user
echo "1. Testing user creation...\n";
$testUserId = 123456789;
$userData = [
    'first_name' => 'تست',
    'last_name' => 'کاربر',
    'username' => 'testuser',
    'step' => 'start'
];

$result = $db->saveUser($testUserId, $userData);
echo "User saved: " . ($result ? "✅ Success" : "❌ Failed") . "\n";

// Test 2: Load the user
echo "\n2. Testing user retrieval...\n";
$loadedUser = $db->getUser($testUserId);
if ($loadedUser) {
    echo "✅ User loaded successfully\n";
    echo "Name: " . $loadedUser['first_name'] . " " . $loadedUser['last_name'] . "\n";
    echo "Step: " . $loadedUser['step'] . "\n";
} else {
    echo "❌ Failed to load user\n";
}

// Test 3: Update user step
echo "\n3. Testing user step update...\n";
$result = $db->updateUserStep($testUserId, 'verified');
echo "Step updated: " . ($result ? "✅ Success" : "❌ Failed") . "\n";

$updatedUser = $db->getUser($testUserId);
if ($updatedUser && $updatedUser['step'] === 'verified') {
    echo "✅ Step verification successful: " . $updatedUser['step'] . "\n";
} else {
    echo "❌ Step verification failed\n";
}

// Test 4: Save a test voucher
echo "\n4. Testing voucher creation...\n";
$voucherId = 'voucher_001';
$voucherData = [
    'code' => 'TEST123',
    'amount' => 100,
    'currency' => 'USD',
    'status' => 'active',
    'created_by' => $testUserId
];

$result = $db->saveVoucher($voucherId, $voucherData);
echo "Voucher saved: " . ($result ? "✅ Success" : "❌ Failed") . "\n";

// Test 5: Find voucher by code
echo "\n5. Testing voucher search by code...\n";
$foundVoucher = $db->findVoucherByCode('TEST123');
if ($foundVoucher) {
    echo "✅ Voucher found successfully\n";
    echo "Code: " . $foundVoucher['code'] . "\n";
    echo "Amount: " . $foundVoucher['amount'] . " " . $foundVoucher['currency'] . "\n";
} else {
    echo "❌ Failed to find voucher\n";
}

// Test 6: Save a transaction
echo "\n6. Testing transaction creation...\n";
$transactionId = 'tx_001';
$transactionData = [
    'user_id' => $testUserId,
    'voucher_id' => $voucherId,
    'amount' => 100,
    'currency' => 'USD',
    'type' => 'voucher_use'
];

$result = $db->saveTransaction($transactionId, $transactionData);
echo "Transaction saved: " . ($result ? "✅ Success" : "❌ Failed") . "\n";

// Test 7: Get database statistics
echo "\n7. Database statistics...\n";
$stats = $db->getStats();
echo "Users: " . $stats['users_count'] . "\n";
echo "Vouchers: " . $stats['vouchers_count'] . "\n";
echo "Transactions: " . $stats['transactions_count'] . "\n";
echo "Groups: " . $stats['groups_count'] . "\n";

// Test 8: List all users
echo "\n8. All users in database...\n";
$allUsers = $db->getAllUsers();
foreach ($allUsers as $user) {
    echo "- User ID: " . $user['id'] . ", Name: " . $user['first_name'] . ", Step: " . $user['step'] . "\n";
}

echo "\n=== Test Complete ===\n";
