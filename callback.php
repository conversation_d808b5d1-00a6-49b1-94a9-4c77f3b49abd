<?php

// Include configuration and database
require_once 'config.php';
require_once 'JsonDatabase.php';

// Initialize JSON Database
$db = new JsonDatabase($JSON_DB_DIR);

// Create logs directory if not exists
if (!file_exists("logs")) {
    mkdir("logs", 0777, true);
}

// Log all incoming data
file_put_contents('logs/callback.log', date('Y-m-d H:i:s') . " Callback received: " . json_encode($_GET) . " POST: " . json_encode($_POST) . "\n", FILE_APPEND);

// Function to send request to Zibal API for verification
function verifyPayment($trackId) {
    $merchantKey = "zibal"; // Same as in payment request
    
    $parameters = array(
        "merchant" => $merchantKey,
        "trackId" => $trackId
    );
    
    $url = 'https://gateway.zibal.ir/v1/verify';
    
    // Log verification request
    file_put_contents('logs/callback.log', date('Y-m-d H:i:s') . " Verification request: " . json_encode($parameters) . "\n", FILE_APPEND);
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($parameters));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    
    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    curl_close($ch);
    
    // Log verification response
    file_put_contents('logs/callback.log', date('Y-m-d H:i:s') . " Verification response (HTTP $httpCode): $result\n", FILE_APPEND);
    
    if ($curlError) {
        file_put_contents('logs/callback.log', date('Y-m-d H:i:s') . " Verification CURL ERROR: $curlError\n", FILE_APPEND);
        return false;
    }
    
    if ($httpCode !== 200) {
        file_put_contents('logs/callback.log', date('Y-m-d H:i:s') . " Verification HTTP ERROR: Code $httpCode\n", FILE_APPEND);
        return false;
    }
    
    return json_decode($result);
}

// Function to send Telegram message
function sendTelegramMessage($chat_id, $text) {
    $url = "https://api.telegram.org/bot" . API_KEY . "/sendMessage";
    
    $data = [
        'chat_id' => $chat_id,
        'text' => $text,
        'parse_mode' => 'HTML'
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    $result = curl_exec($ch);
    curl_close($ch);
    
    return $result;
}

// Function to generate vouchers for user
function generateVouchers($user_id, $count) {
    global $db;
    
    $vouchers = [];
    for ($i = 0; $i < $count; $i++) {
        $token = $db->generateUserToken($user_id);
        $voucherData = [
            'user_id' => $user_id,
            'token' => $token,
            'amount' => 10000, // 10k Toman per voucher
            'status' => 'active',
            'created_at' => time(),
            'used_at' => null
        ];
        
        $db->saveVoucher($token, $voucherData);
        $vouchers[] = $token;
    }
    
    return $vouchers;
}

// Get callback parameters
$success = isset($_GET['success']) ? $_GET['success'] : null;
$status = isset($_GET['status']) ? $_GET['status'] : null;
$trackId = isset($_GET['trackId']) ? $_GET['trackId'] : null;
$orderId = isset($_GET['orderId']) ? $_GET['orderId'] : null;

// Check if required parameters exist
if (!$trackId || !$orderId) {
    file_put_contents('logs/callback.log', date('Y-m-d H:i:s') . " ERROR: Missing required parameters\n", FILE_APPEND);
    echo "خطا: پارامترهای مورد نیاز موجود نیست";
    exit;
}

// Get order information
$orderData = $db->load('orders', $orderId);

if (!$orderData) {
    file_put_contents('logs/callback.log', date('Y-m-d H:i:s') . " ERROR: Order not found: $orderId\n", FILE_APPEND);
    echo "خطا: سفارش یافت نشد";
    exit;
}

$user_id = $orderData['user_id'];
$voucher_count = $orderData['voucher_count'];
$amount = $orderData['amount'];

// Check payment status
if ($success == '1' && $status == '2') {
    // Payment seems successful, verify with Zibal
    $verification = verifyPayment($trackId);
    
    if ($verification && isset($verification->result) && $verification->result == 100) {
        // Payment verified successfully
        file_put_contents('logs/callback.log', date('Y-m-d H:i:s') . " Payment verified successfully for order $orderId\n", FILE_APPEND);
        
        // Update order status
        $orderData['status'] = 'completed';
        $orderData['completed_at'] = time();
        $orderData['track_id'] = $trackId;
        $db->save('orders', $orderId, $orderData);
        
        // Generate vouchers
        $vouchers = generateVouchers($user_id, $voucher_count);
        
        // Send success message to user
        $message = "✅ پرداخت با موفقیت انجام شد!\n\n";
        $message .= "🎫 ووچرهای شما:\n\n";
        
        foreach ($vouchers as $index => $voucher) {
            $message .= "ووچر " . ($index + 1) . ":\n";
            $message .= "<code>$voucher</code>\n\n";
        }
        
        $message .= "💰 مبلغ پرداختی: " . number_format($amount) . " تومان\n";
        $message .= "📅 تاریخ: " . date('Y/m/d H:i', time()) . "\n\n";
        $message .= "🔸 ووچرهای شما آماده استفاده هستند.";
        
        sendTelegramMessage($user_id, $message);
        
        echo "پرداخت با موفقیت انجام شد! ووچرهای شما در ربات ارسال شده است.";
        
    } else {
        // Payment verification failed
        $errorCode = isset($verification->result) ? $verification->result : 'unknown';
        file_put_contents('logs/callback.log', date('Y-m-d H:i:s') . " Payment verification failed for order $orderId. Error code: $errorCode\n", FILE_APPEND);
        
        // Update order status
        $orderData['status'] = 'failed';
        $orderData['failed_at'] = time();
        $orderData['track_id'] = $trackId;
        $orderData['error_code'] = $errorCode;
        $db->save('orders', $orderId, $orderData);
        
        // Send failure message to user
        $message = "❌ خطا در تأیید پرداخت\n\n";
        $message .= "متأسفانه پرداخت شما تأیید نشد.\n";
        $message .= "در صورت کسر مبلغ از حساب، طی 24 ساعت بازگردانده خواهد شد.\n\n";
        $message .= "شماره پیگیری: $trackId";
        
        sendTelegramMessage($user_id, $message);
        
        echo "خطا در تأیید پرداخت. لطفاً با پشتیبانی تماس بگیرید.";
    }
} else {
    // Payment failed or cancelled
    file_put_contents('logs/callback.log', date('Y-m-d H:i:s') . " Payment failed or cancelled for order $orderId. Success: $success, Status: $status\n", FILE_APPEND);
    
    // Update order status
    $orderData['status'] = 'cancelled';
    $orderData['cancelled_at'] = time();
    $orderData['track_id'] = $trackId;
    $db->save('orders', $orderId, $orderData);
    
    // Send cancellation message to user
    $message = "❌ پرداخت لغو شد\n\n";
    $message .= "پرداخت شما لغو شده یا با خطا مواجه شده است.\n";
    $message .= "می‌توانید مجدداً تلاش کنید.\n\n";
    $message .= "شماره پیگیری: $trackId";
    
    sendTelegramMessage($user_id, $message);
    
    echo "پرداخت لغو شد. می‌توانید مجدداً تلاش کنید.";
}

?>
